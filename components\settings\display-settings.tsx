'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Group as RadioGroup,
  Item as RadioGroupItem,
} from '@/components/ui/radio';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { useSettings } from '@/lib/hooks';
import { UserSettings } from '@/lib/types/database-modules';
import { Loader2, Monitor, Moon, Sun } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface DisplaySettingsProps {
  initialSettings: UserSettings | null;
}

export function DisplaySettings({ initialSettings }: DisplaySettingsProps) {
  const { updateDisplaySettings } = useSettings();
  const [isSaving, setIsSaving] = useState(false);

  // State for display settings
  const [theme, setTheme] = useState<string>(initialSettings?.theme ?? 'light');

  const [language, setLanguage] = useState<string>(
    initialSettings?.language ?? 'en'
  );

  const [documentView, setDocumentView] = useState<'card' | 'table'>(
    initialSettings?.display_preferences?.document_view ?? 'card'
  );

  const [showPreviews, setShowPreviews] = useState<boolean>(
    initialSettings?.display_preferences?.show_document_previews ?? true
  );

  const [fontSize, setFontSize] = useState<'small' | 'medium' | 'large'>(
    initialSettings?.display_preferences?.fontSize ?? 'medium'
  );

  const [reduceMotion, setReduceMotion] = useState<boolean>(
    initialSettings?.display_preferences?.reduceMotion ?? false
  );

  const [highContrast, setHighContrast] = useState<boolean>(
    initialSettings?.display_preferences?.highContrast ?? false
  );

  // Font settings have been removed

  // Handle saving display settings
  const saveDisplaySettings = async () => {
    setIsSaving(true);

    const updatePromise = updateDisplaySettings({
      theme,
      language,
      display_preferences: {
        document_view: documentView,
        show_document_previews: showPreviews,
        fontSize,
        reduceMotion,
        highContrast,
        sidebar_collapsed:
          initialSettings?.display_preferences?.sidebar_collapsed ?? false,
      },
    });

    toast.promise(updatePromise, {
      loading: 'Saving display settings...',
      success: 'Display settings saved successfully!',
      error: 'Failed to save display settings',
    });

    try {
      await updatePromise;
    } catch (error) {
      console.error('Error saving display settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // If no settings are provided, show skeleton
  if (!initialSettings) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-6 w-10" />
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Display Settings</CardTitle>
        <CardDescription>
          Customize how the application looks and feels
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label>Theme</Label>
          <RadioGroup
            value={theme}
            onValueChange={setTheme}
            className="flex space-x-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="light" id="theme-light" />
              <Label htmlFor="theme-light" className="flex items-center">
                <Sun className="mr-2 h-4 w-4" />
                Light
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <RadioGroupItem value="dark" id="theme-dark" />
              <Label htmlFor="theme-dark" className="flex items-center">
                <Moon className="mr-2 h-4 w-4" />
                Dark
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <RadioGroupItem value="system" id="theme-system" />
              <Label htmlFor="theme-system" className="flex items-center">
                <Monitor className="mr-2 h-4 w-4" />
                System
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-2">
          <Label htmlFor="language">Language</Label>
          <Select value={language} onValueChange={setLanguage}>
            <SelectTrigger id="language">
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="es">Español</SelectItem>
              <SelectItem value="fr">Français</SelectItem>
              <SelectItem value="de">Deutsch</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="document-view">Document View</Label>
          <Select
            value={documentView}
            onValueChange={(value: 'card' | 'table') => setDocumentView(value)}
          >
            <SelectTrigger id="document-view">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="card">Card View</SelectItem>
              <SelectItem value="table">Table View</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="font-size">Font Size</Label>
          <Select
            value={fontSize}
            onValueChange={(value: 'small' | 'medium' | 'large') =>
              setFontSize(value)
            }
          >
            <SelectTrigger id="font-size">
              <SelectValue placeholder="Select size" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="small">Small</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="large">Large</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Font settings have been removed */}

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="show-previews">Show Document Previews</Label>
            <p className="text-sm text-muted-foreground">
              Display document previews in the document list
            </p>
          </div>
          <Switch
            id="show-previews"
            checked={showPreviews}
            onCheckedChange={setShowPreviews}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="reduce-motion">Reduce Motion</Label>
            <p className="text-sm text-muted-foreground">
              Minimize animations throughout the interface
            </p>
          </div>
          <Switch
            id="reduce-motion"
            checked={reduceMotion}
            onCheckedChange={setReduceMotion}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="high-contrast">High Contrast</Label>
            <p className="text-sm text-muted-foreground">
              Increase contrast for better visibility
            </p>
          </div>
          <Switch
            id="high-contrast"
            checked={highContrast}
            onCheckedChange={setHighContrast}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={saveDisplaySettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
