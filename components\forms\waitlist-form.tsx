'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Group as RadioGroup,
  Item as RadioGroupItem,
} from '@/components/ui/radio';
import { FONT_JETBRAINS_MONO } from '@/lib/constants';
import { useWaitlist } from '@/lib/hooks/use-waitlist';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const WaitlistSchema = z.object({
  full_name: z
    .string()
    .min(2, { message: 'Full name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  role: z.enum(['user', 'lawyer'], { message: 'Please select a role' }),
});

type WaitlistFormData = z.infer<typeof WaitlistSchema>;

export function WaitlistForm() {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { joinWaitlist, isLoading } = useWaitlist();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<WaitlistFormData>({
    resolver: zodResolver(WaitlistSchema),
  });

  const selectedRole = watch('role');

  const onSubmit = async (data: WaitlistFormData) => {
    toast.promise(joinWaitlist(data), {
      loading: 'Joining waitlist...',
      success: () => {
        setIsSubmitted(true);
        return 'Successfully joined the waitlist!';
      },
      error: (err) => err.message || 'Failed to join waitlist',
    });
  };

  if (isSubmitted) {
    return (
      <div className="text-center space-y-4 p-6 border border-green-200 bg-green-50 rounded-lg">
        <div className="text-green-600 text-2xl">🎉</div>
        <h3 className="text-lg font-semibold text-green-800">
          You're on the list!
        </h3>
        <p className="text-green-700">
          Thank you for joining our waitlist. We'll notify you as soon as
          Notamess Forms is ready!
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="full_name" className="text-sm font-medium">
          Full Name
        </Label>
        <Input
          id="full_name"
          type="text"
          placeholder="Enter your full name"
          {...register('full_name')}
          className={cn(
            'w-full',
            errors.full_name && 'border-red-500 focus:border-red-500'
          )}
        />
        {errors.full_name && (
          <p className="text-sm text-red-500">{errors.full_name.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium">
          Email Address
        </Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email address"
          {...register('email')}
          className={cn(
            'w-full',
            errors.email && 'border-red-500 focus:border-red-500'
          )}
        />
        {errors.email && (
          <p className="text-sm text-red-500">{errors.email.message}</p>
        )}
      </div>

      <div className="space-y-3">
        <Label className="text-sm font-medium">I am a:</Label>
        <RadioGroup
          value={selectedRole}
          onValueChange={(value) =>
            setValue('role', value as 'user' | 'lawyer')
          }
          className="space-y-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="user" id="user" />
            <Label htmlFor="user" className="cursor-pointer">
              Regular User - I need help with legal documents
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="lawyer" id="lawyer" />
            <Label htmlFor="lawyer" className="cursor-pointer">
              Lawyer - I want to provide legal services
            </Label>
          </div>
        </RadioGroup>
        {errors.role && (
          <p className="text-sm text-red-500">{errors.role.message}</p>
        )}
      </div>

      <Button
        type="submit"
        disabled={isLoading}
        className={cn(
          FONT_JETBRAINS_MONO.className,
          'w-full font-bold uppercase',
          'bg-accent-300 hover:bg-accent-400 text-white'
        )}
      >
        {isLoading ? 'Joining...' : 'Join Waitlist'}
      </Button>
    </form>
  );
}
